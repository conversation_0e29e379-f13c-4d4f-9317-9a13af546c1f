#!/bin/bash

# 分支代码差异对比脚本
# 对比 origin/phase2 和 origin/master 的代码差异并输出相关提交信息

echo "=========================================="
echo "分支代码差异对比工具"
echo "对比分支: origin/phase2 vs origin/master"
echo "=========================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查Git仓库
if ! git rev-parse --git-dir > /dev/null 2>&1; then
    echo -e "${RED}错误: 当前目录不是Git仓库${NC}"
    exit 1
fi

# 更新远程分支信息
echo -e "${BLUE}更新远程分支信息...${NC}"
git fetch origin

# 检查分支是否存在
echo -e "${BLUE}检查分支状态...${NC}"
if ! git rev-parse --verify origin/phase2 >/dev/null 2>&1; then
    echo -e "${RED}错误: origin/phase2 分支不存在${NC}"
    exit 1
fi

if ! git rev-parse --verify origin/master >/dev/null 2>&1; then
    echo -e "${RED}错误: origin/master 分支不存在${NC}"
    exit 1
fi

# 获取分支的最新提交信息
echo -e "\n${YELLOW}=== 分支基本信息 ===${NC}"
echo -e "${GREEN}origin/phase2 最新提交:${NC}"
git log origin/phase2 -1 --pretty=format:"%h - %an, %ad : %s" --date=format:'%Y-%m-%d %H:%M:%S'
echo -e "\n${GREEN}origin/master 最新提交:${NC}"
git log origin/master -1 --pretty=format:"%h - %an, %ad : %s" --date=format:'%Y-%m-%d %H:%M:%S'

# 检查整体差异
echo -e "\n\n${YELLOW}=== 整体差异统计 ===${NC}"
if git diff --quiet origin/phase2..origin/master; then
    echo -e "${GREEN}✓ 两个分支的代码完全相同，无差异${NC}"
    exit 0
else
    echo -e "${RED}发现代码差异:${NC}"
    git diff origin/phase2..origin/master --stat
fi

# 获取有差异的文件列表
echo -e "\n${YELLOW}=== 有差异的文件列表 ===${NC}"
changed_files=$(git diff origin/phase2..origin/master --name-only)
if [ -z "$changed_files" ]; then
    echo -e "${GREEN}无文件差异${NC}"
    exit 0
fi

file_count=$(echo "$changed_files" | wc -l)
echo -e "${BLUE}差异文件数量: $file_count${NC}"
echo "$changed_files"

# 分析每个有差异的文件的提交历史
echo -e "\n${YELLOW}=== 差异文件的相关提交信息 ===${NC}"

counter=0
echo "$changed_files" | while read -r file; do
    if [ -n "$file" ]; then
        counter=$((counter + 1))
        echo -e "\n${GREEN}[$counter] 文件: $file${NC}"
        echo -e "${BLUE}----------------------------------------${NC}"
        
        # 检查文件在两个分支中的最后修改提交
        echo -e "${YELLOW}在 origin/phase2 中的最后修改:${NC}"
        phase2_commit=$(git log origin/phase2 -1 --pretty=format:"%h - %an, %ad : %s" --date=format:'%Y-%m-%d %H:%M:%S' -- "$file" 2>/dev/null)
        if [ -n "$phase2_commit" ]; then
            echo "  $phase2_commit"
        else
            echo "  文件在此分支中不存在或无提交记录"
        fi
        
        echo -e "${YELLOW}在 origin/master 中的最后修改:${NC}"
        master_commit=$(git log origin/master -1 --pretty=format:"%h - %an, %ad : %s" --date=format:'%Y-%m-%d %H:%M:%S' -- "$file" 2>/dev/null)
        if [ -n "$master_commit" ]; then
            echo "  $master_commit"
        else
            echo "  文件在此分支中不存在或无提交记录"
        fi
        
        # 显示文件的具体差异统计
        echo -e "${YELLOW}差异统计:${NC}"
        git diff origin/phase2..origin/master --stat -- "$file" | sed 's/^/  /'
        
        # 显示该文件相关的最近几次提交
        echo -e "${YELLOW}该文件最近的相关提交 (最近5次):${NC}"
        echo -e "${BLUE}  在 phase2 分支:${NC}"
        git log origin/phase2 -5 --pretty=format:"    %h - %an, %ad : %s" --date=format:'%Y-%m-%d %H:%M:%S' -- "$file" 2>/dev/null || echo "    无相关提交"
        
        echo -e "${BLUE}  在 master 分支:${NC}"
        git log origin/master -5 --pretty=format:"    %h - %an, %ad : %s" --date=format:'%Y-%m-%d %H:%M:%S' -- "$file" 2>/dev/null || echo "    无相关提交"
    fi
done

# 显示分支独有的提交
echo -e "\n${YELLOW}=== 分支独有的提交 ===${NC}"

echo -e "\n${GREEN}origin/phase2 独有的提交 (不在 master 中):${NC}"
phase2_only=$(git log origin/master..origin/phase2 --pretty=format:"%h - %an, %ad : %s" --date=format:'%Y-%m-%d %H:%M:%S' --no-merges)
if [ -n "$phase2_only" ]; then
    echo "$phase2_only" | sed 's/^/  /'
else
    echo "  无独有提交"
fi

echo -e "\n${GREEN}origin/master 独有的提交 (不在 phase2 中):${NC}"
master_only=$(git log origin/phase2..origin/master --pretty=format:"%h - %an, %ad : %s" --date=format:'%Y-%m-%d %H:%M:%S' --no-merges)
if [ -n "$master_only" ]; then
    echo "$master_only" | sed 's/^/  /'
else
    echo "  无独有提交"
fi

# 查找可能的cherry-pick提交
echo -e "\n${YELLOW}=== Cherry-pick 分析 ===${NC}"
echo -e "${BLUE}检查可能已经cherry-pick但有差异的提交...${NC}"
cherry_picks=$(git log origin/phase2...origin/master --cherry-pick --right-only --no-merges --pretty=format:"%h - %an, %ad : %s" --date=format:'%Y-%m-%d %H:%M:%S')
if [ -n "$cherry_picks" ]; then
    echo -e "${YELLOW}可能的cherry-pick提交:${NC}"
    echo "$cherry_picks" | sed 's/^/  /'
else
    echo "  无cherry-pick相关差异"
fi

echo -e "\n${GREEN}=========================================="
echo "分支对比完成!"
echo "==========================================${NC}"

# 生成报告文件
report_file="branch_diff_report_$(date +%Y%m%d_%H%M%S).txt"
echo -e "\n${BLUE}生成详细报告文件: $report_file${NC}"

{
    echo "分支代码差异报告"
    echo "生成时间: $(date)"
    echo "对比分支: origin/phase2 vs origin/master"
    echo "========================================"
    echo ""
    
    echo "=== 分支基本信息 ==="
    echo "origin/phase2 最新提交:"
    git log origin/phase2 -1 --pretty=format:"%h - %an, %ad : %s" --date=format:'%Y-%m-%d %H:%M:%S'
    echo ""
    echo "origin/master 最新提交:"
    git log origin/master -1 --pretty=format:"%h - %an, %ad : %s" --date=format:'%Y-%m-%d %H:%M:%S'
    echo ""
    
    echo "=== 整体差异统计 ==="
    git diff origin/phase2..origin/master --stat
    echo ""
    
    echo "=== 有差异的文件列表 ==="
    echo "$changed_files"
    echo ""
    
    echo "=== 分支独有的提交 ==="
    echo "origin/phase2 独有的提交:"
    git log origin/master..origin/phase2 --pretty=format:"%h - %an, %ad : %s" --date=format:'%Y-%m-%d %H:%M:%S' --no-merges
    echo ""
    echo "origin/master 独有的提交:"
    git log origin/phase2..origin/master --pretty=format:"%h - %an, %ad : %s" --date=format:'%Y-%m-%d %H:%M:%S' --no-merges
    echo ""
    
} > "$report_file"

echo -e "${GREEN}报告已保存到: $report_file${NC}"
