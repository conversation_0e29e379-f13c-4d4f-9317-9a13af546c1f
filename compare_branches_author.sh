#!/bin/bash

# 分支代码差异对比脚本 - 针对特定作者
# 对比 origin/phase2 和 origin/master 中指定作者的代码差异

AUTHOR="池启苹"

echo "=========================================="
echo "分支代码差异对比工具 - 作者: $AUTHOR"
echo "对比分支: origin/phase2 vs origin/master"
echo "=========================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查Git仓库
if ! git rev-parse --git-dir > /dev/null 2>&1; then
    echo -e "${RED}错误: 当前目录不是Git仓库${NC}"
    exit 1
fi

# 检查分支是否存在
echo -e "${BLUE}检查分支状态...${NC}"
if ! git rev-parse --verify origin/phase2 >/dev/null 2>&1; then
    echo -e "${RED}错误: origin/phase2 分支不存在${NC}"
    exit 1
fi

if ! git rev-parse --verify origin/master >/dev/null 2>&1; then
    echo -e "${RED}错误: origin/master 分支不存在${NC}"
    exit 1
fi

# 获取指定作者在两个分支中的独有提交
echo -e "\n${YELLOW}=== $AUTHOR 在各分支的独有提交 ===${NC}"

echo -e "\n${GREEN}$AUTHOR 在 origin/phase2 中的独有提交:${NC}"
phase2_commits=$(git log origin/master..origin/phase2 --author="$AUTHOR" --no-merges --pretty=format:"%H|%h|%an|%ad|%s" --date=format:'%Y-%m-%d %H:%M:%S')
if [ -n "$phase2_commits" ]; then
    echo "$phase2_commits" | while IFS='|' read -r full_hash short_hash author date subject; do
        echo "  $short_hash - $author, $date : $subject"
    done
else
    echo "  无独有提交"
fi

echo -e "\n${GREEN}$AUTHOR 在 origin/master 中的独有提交:${NC}"
master_commits=$(git log origin/phase2..origin/master --author="$AUTHOR" --no-merges --pretty=format:"%H|%h|%an|%ad|%s" --date=format:'%Y-%m-%d %H:%M:%S')
if [ -n "$master_commits" ]; then
    echo "$master_commits" | while IFS='|' read -r full_hash short_hash author date subject; do
        echo "  $short_hash - $author, $date : $subject"
    done
else
    echo "  无独有提交"
fi

# 分析每个独有提交涉及的文件差异
echo -e "\n${YELLOW}=== $AUTHOR 独有提交的文件差异分析 ===${NC}"

# 处理phase2独有的提交
if [ -n "$phase2_commits" ]; then
    echo -e "\n${BLUE}分析 phase2 独有提交涉及的文件:${NC}"
    echo "$phase2_commits" | while IFS='|' read -r full_hash short_hash author date subject; do
        echo -e "\n${GREEN}提交: $short_hash - $subject${NC}"
        
        # 获取该提交涉及的文件
        changed_files=$(git show --name-only --pretty=format: "$full_hash" | grep -v '^$')
        if [ -n "$changed_files" ]; then
            echo -e "${YELLOW}  涉及的文件:${NC}"
            echo "$changed_files" | while read -r file; do
                if [ -n "$file" ]; then
                    echo "    - $file"
                    
                    # 检查该文件在两个分支中是否有差异
                    if ! git diff --quiet origin/phase2..origin/master -- "$file" 2>/dev/null; then
                        echo -e "      ${RED}[有差异]${NC} 该文件在两个分支中不同"
                        
                        # 显示该文件在master分支中的最后修改者
                        master_last_commit=$(git log origin/master -1 --pretty=format:"%h - %an, %ad : %s" --date=format:'%Y-%m-%d %H:%M:%S' -- "$file" 2>/dev/null)
                        if [ -n "$master_last_commit" ]; then
                            echo "      ${BLUE}Master分支最后修改:${NC} $master_last_commit"
                        fi
                        
                        # 显示差异统计
                        diff_stat=$(git diff origin/phase2..origin/master --stat -- "$file" 2>/dev/null)
                        if [ -n "$diff_stat" ]; then
                            echo "      ${YELLOW}差异统计:${NC} $diff_stat"
                        fi
                    else
                        echo -e "      ${GREEN}[无差异]${NC} 该文件在两个分支中相同"
                    fi
                fi
            done
        else
            echo "    无文件变更"
        fi
    done
fi

# 处理master独有的提交
if [ -n "$master_commits" ]; then
    echo -e "\n${BLUE}分析 master 独有提交涉及的文件:${NC}"
    echo "$master_commits" | while IFS='|' read -r full_hash short_hash author date subject; do
        echo -e "\n${GREEN}提交: $short_hash - $subject${NC}"
        
        # 获取该提交涉及的文件
        changed_files=$(git show --name-only --pretty=format: "$full_hash" | grep -v '^$')
        if [ -n "$changed_files" ]; then
            echo -e "${YELLOW}  涉及的文件:${NC}"
            echo "$changed_files" | while read -r file; do
                if [ -n "$file" ]; then
                    echo "    - $file"
                    
                    # 检查该文件在两个分支中是否有差异
                    if ! git diff --quiet origin/phase2..origin/master -- "$file" 2>/dev/null; then
                        echo -e "      ${RED}[有差异]${NC} 该文件在两个分支中不同"
                        
                        # 显示该文件在phase2分支中的最后修改者
                        phase2_last_commit=$(git log origin/phase2 -1 --pretty=format:"%h - %an, %ad : %s" --date=format:'%Y-%m-%d %H:%M:%S' -- "$file" 2>/dev/null)
                        if [ -n "$phase2_last_commit" ]; then
                            echo "      ${BLUE}Phase2分支最后修改:${NC} $phase2_last_commit"
                        fi
                        
                        # 显示差异统计
                        diff_stat=$(git diff origin/phase2..origin/master --stat -- "$file" 2>/dev/null)
                        if [ -n "$diff_stat" ]; then
                            echo "      ${YELLOW}差异统计:${NC} $diff_stat"
                        fi
                    else
                        echo -e "      ${GREEN}[无差异]${NC} 该文件在两个分支中相同"
                    fi
                fi
            done
        else
            echo "    无文件变更"
        fi
    done
fi

# 查找可能的cherry-pick提交（相同内容但不同哈希）
echo -e "\n${YELLOW}=== $AUTHOR 可能的 Cherry-pick 提交分析 ===${NC}"
echo -e "${BLUE}检查可能已经cherry-pick但有差异的提交...${NC}"

# 使用cherry-pick检测
cherry_picks=$(git log origin/phase2...origin/master --cherry-pick --right-only --author="$AUTHOR" --no-merges --pretty=format:"%h - %an, %ad : %s" --date=format:'%Y-%m-%d %H:%M:%S')
if [ -n "$cherry_picks" ]; then
    echo -e "${YELLOW}$AUTHOR 可能的cherry-pick提交:${NC}"
    echo "$cherry_picks" | while read -r commit_info; do
        echo "  $commit_info"
    done
else
    echo "  无cherry-pick相关差异"
fi

# 生成针对该作者的详细报告
echo -e "\n${YELLOW}=== 生成详细报告 ===${NC}"
report_file="author_diff_report_${AUTHOR}_$(date +%Y%m%d_%H%M%S).txt"
echo -e "${BLUE}生成针对 $AUTHOR 的详细报告文件: $report_file${NC}"

{
    echo "作者代码差异报告 - $AUTHOR"
    echo "生成时间: $(date)"
    echo "对比分支: origin/phase2 vs origin/master"
    echo "========================================"
    echo ""
    
    echo "=== $AUTHOR 在各分支的独有提交 ==="
    echo ""
    echo "$AUTHOR 在 origin/phase2 中的独有提交:"
    if [ -n "$phase2_commits" ]; then
        echo "$phase2_commits" | while IFS='|' read -r full_hash short_hash author date subject; do
            echo "  $short_hash - $author, $date : $subject"
        done
    else
        echo "  无独有提交"
    fi
    echo ""
    
    echo "$AUTHOR 在 origin/master 中的独有提交:"
    if [ -n "$master_commits" ]; then
        echo "$master_commits" | while IFS='|' read -r full_hash short_hash author date subject; do
            echo "  $short_hash - $author, $date : $subject"
        done
    else
        echo "  无独有提交"
    fi
    echo ""
    
    echo "=== Cherry-pick 分析 ==="
    if [ -n "$cherry_picks" ]; then
        echo "$AUTHOR 可能的cherry-pick提交:"
        echo "$cherry_picks"
    else
        echo "无cherry-pick相关差异"
    fi
    echo ""
    
} > "$report_file"

echo -e "\n${GREEN}=========================================="
echo "针对 $AUTHOR 的分支对比完成!"
echo "详细报告已保存到: $report_file"
echo "==========================================${NC}"
